package io.gigsta.presentation.home

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

data class HistoryItem(
    val title: String,
    val subtitle: String,
    val progress: Float,
    val date: String
)

data class MenuItem(
    val title: String,
    val icon: ImageVector,
    val historyItems: List<HistoryItem>
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen() {
    val menuItems = remember {
        listOf(
            MenuItem(
                title = "CV Builder",
                icon = Icons.Default.Person,
                historyItems = listOf(
                    HistoryItem("UI/UX Designer", "Senior", 0.85f, "11 May 2024"),
                    HistoryItem("Graphic Designer", "Mid-level", 0.56f, "11 May 2024"),
                    HistoryItem("Product Designer", "Junior", 0.28f, "11 May 2024"),
                    HistoryItem("Designer", "Entry", 0.19f, "22 May 2024")
                )
            ),
            MenuItem(
                title = "Application Letter",
                icon = Icons.Default.Email,
                historyItems = listOf(
                    HistoryItem("Google Application", "Software Engineer", 1.0f, "15 May 2024"),
                    HistoryItem("Meta Cover Letter", "Product Manager", 0.75f, "12 May 2024"),
                    HistoryItem("Apple Letter", "Designer", 0.45f, "10 May 2024")
                )
            ),
            MenuItem(
                title = "Email Application",
                icon = Icons.Default.Send,
                historyItems = listOf(
                    HistoryItem("Follow-up Email", "Google", 1.0f, "16 May 2024"),
                    HistoryItem("Thank You Email", "Meta", 1.0f, "13 May 2024"),
                    HistoryItem("Introduction Email", "Apple", 0.60f, "11 May 2024")
                )
            ),
            MenuItem(
                title = "Job Match",
                icon = Icons.Default.Search,
                historyItems = listOf(
                    HistoryItem("Senior Developer", "95% match", 0.95f, "Today"),
                    HistoryItem("Product Manager", "87% match", 0.87f, "Yesterday"),
                    HistoryItem("UX Designer", "72% match", 0.72f, "2 days ago")
                )
            )
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Resume",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { /* Handle menu */ }) {
                        Icon(Icons.Default.Menu, contentDescription = "Menu")
                    }
                },
                actions = {
                    IconButton(onClick = { /* Handle search */ }) {
                        Icon(Icons.Default.Search, contentDescription = "Search")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            )
        },
        bottomBar = {
            BottomNavigationBar()
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            items(menuItems) { menuItem ->
                MenuSection(menuItem = menuItem)
            }
            
            // Add some bottom padding
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

@Composable
fun MenuSection(menuItem: MenuItem) {
    Column {
        // Menu Header
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { /* Handle menu click */ }
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = menuItem.icon,
                contentDescription = menuItem.title,
                modifier = Modifier.size(24.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = menuItem.title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            Spacer(modifier = Modifier.weight(1f))
            Icon(
                imageVector = Icons.Default.KeyboardArrowRight,
                contentDescription = "View all",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        // History Items
        menuItem.historyItems.take(3).forEach { historyItem ->
            HistoryItemCard(historyItem = historyItem)
            Spacer(modifier = Modifier.height(8.dp))
        }
        
        if (menuItem.historyItems.size > 3) {
            Text(
                text = "View all ${menuItem.historyItems.size} items",
                color = MaterialTheme.colorScheme.primary,
                fontSize = 14.sp,
                modifier = Modifier
                    .clickable { /* Handle view all */ }
                    .padding(vertical = 8.dp)
            )
        }
    }
}

@Composable
fun HistoryItemCard(historyItem: HistoryItem) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { /* Handle item click */ },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Document icon placeholder
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // Content
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = historyItem.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = historyItem.subtitle,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = historyItem.date,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Progress indicator
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "${(historyItem.progress * 100).toInt()}%",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.height(4.dp))
                LinearProgressIndicator(
                    progress = { historyItem.progress },
                    modifier = Modifier.width(60.dp),
                    color = MaterialTheme.colorScheme.primary,
                    trackColor = MaterialTheme.colorScheme.surfaceVariant,
                )
            }
        }
    }
}

@Composable
fun BottomNavigationBar() {
    NavigationBar(
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp
    ) {
        NavigationBarItem(
            icon = { Icon(Icons.Default.Home, contentDescription = "Home") },
            label = { Text("Home") },
            selected = true,
            onClick = { /* Handle home click */ }
        )
        NavigationBarItem(
            icon = { Icon(Icons.Default.Star, contentDescription = "Template") },
            label = { Text("Template") },
            selected = false,
            onClick = { /* Handle template click */ }
        )
        NavigationBarItem(
            icon = { Icon(Icons.Default.Favorite, contentDescription = "Favorites") },
            label = { Text("Favorites") },
            selected = false,
            onClick = { /* Handle favorites click */ }
        )
        NavigationBarItem(
            icon = { Icon(Icons.Default.Settings, contentDescription = "Settings") },
            label = { Text("Settings") },
            selected = false,
            onClick = { /* Handle settings click */ }
        )
    }
}
